# -*- coding: utf-8 -*-
import requests
import os
import sys
from dotenv import load_dotenv

# 设置编码
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

# 加载环境变量
load_dotenv()

class WindowsAIChat:
    def __init__(self):
        self.base_url = "https://models.inference.ai.azure.com"
        self.token = os.getenv("GITHUB_TOKEN")
        if not self.token:
            raise ValueError("请设置 GITHUB_TOKEN 环境变量")
        
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        self.models = {
            "1": "gpt-4o-mini",
            "2": "gpt-4o", 
            "3": "gpt-3.5-turbo",
            "4": "Phi-3-mini-4k-instruct"
        }
    
    def chat_completion(self, model, messages, max_tokens=1000, temperature=0.7):
        """发送聊天完成请求"""
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        try:
            response = requests.post(url, json=data, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def show_menu(self):
        """显示功能菜单"""
        print("\n" + "="*50)
        print("AI助手 - 选择功能")
        print("="*50)
        print("1. 编程助手")
        print("2. 写作助手") 
        print("3. 翻译助手")
        print("4. 学习助手")
        print("5. 实用工具")
        print("6. 创意助手")
        print("7. 自由对话")
        print("0. 退出")
        print("="*50)
    
    def select_model(self):
        """选择AI模型"""
        print("\n选择AI模型:")
        print("1. GPT-4o-mini (推荐)")
        print("2. GPT-4o (最强大)")
        print("3. GPT-3.5-turbo")
        print("4. Phi-3-mini")
        
        while True:
            choice = input("\n请选择模型 (1-4): ").strip()
            if choice in self.models:
                return self.models[choice]
            print("无效选择，请重新输入")
    
    def ask_question(self, prompt_type):
        """根据类型询问问题"""
        prompts = {
            "programming": "请描述你的编程需求: ",
            "writing": "请告诉我你想写什么: ",
            "translation": "请输入要翻译的文本: ",
            "learning": "请问你想了解什么: ",
            "utility": "请描述你需要的工具: ",
            "creative": "请告诉我你的创意需求: ",
            "chat": "想聊什么: "
        }
        return input(prompts.get(prompt_type, "请输入你的问题: "))
    
    def get_system_message(self, function_type):
        """获取系统消息"""
        systems = {
            "programming": "你是专业的编程助手，提供清晰实用的代码和解释。",
            "writing": "你是专业的写作助手，擅长各种文体写作。",
            "translation": "你是专业的翻译助手，准确翻译并解释要点。",
            "learning": "你是耐心的老师，善于简单易懂地解释概念。",
            "utility": "你是实用工具专家，擅长创建自动化脚本。",
            "creative": "你是富有创意的助手，擅长创作有趣内容。",
            "chat": "你是友好的AI助手。"
        }
        return systems.get(function_type, "你是有用的AI助手。")
    
    def process_request(self, choice, model):
        """处理用户请求"""
        function_map = {
            "1": "programming",
            "2": "writing", 
            "3": "translation",
            "4": "learning",
            "5": "utility",
            "6": "creative",
            "7": "chat"
        }
        
        function_type = function_map[choice]
        user_input = self.ask_question(function_type)
        
        messages = [
            {"role": "system", "content": self.get_system_message(function_type)},
            {"role": "user", "content": user_input}
        ]
        
        # 特殊处理翻译
        if function_type == "translation":
            messages[1]["content"] = f"请翻译以下文本并解释要点: {user_input}"
        
        return self.chat_completion(model, messages, max_tokens=800, temperature=0.6)
    
    def run(self):
        """主运行循环"""
        print("欢迎使用AI助手!")
        
        while True:
            try:
                self.show_menu()
                choice = input("请选择功能 (0-7): ").strip()
                
                if choice == "0":
                    print("再见!")
                    break
                
                if choice not in ["1", "2", "3", "4", "5", "6", "7"]:
                    print("无效选择，请重新输入")
                    continue
                
                model = self.select_model()
                print(f"\n使用模型: {model}")
                
                result = self.process_request(choice, model)
                
                # 显示结果
                print("\n" + "="*50)
                print("AI回复:")
                print("="*50)
                
                if "error" in result:
                    print(f"错误: {result['error']}")
                else:
                    print(result["choices"][0]["message"]["content"])
                    print(f"\n使用tokens: {result['usage']['total_tokens']}")
                
                print("\n" + "="*50)
                input("按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"发生错误: {e}")
                input("按回车键继续...")

if __name__ == "__main__":
    try:
        chat = WindowsAIChat()
        chat.run()
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
