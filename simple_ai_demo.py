import requests
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def call_ai(prompt, model="gpt-4o-mini"):
    """简单的AI调用函数"""
    base_url = "https://models.inference.ai.azure.com"
    token = os.getenv("GITHUB_TOKEN")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": 500,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(f"{base_url}/chat/completions", json=data, headers=headers)
        response.raise_for_status()
        result = response.json()
        return result["choices"][0]["message"]["content"]
    except Exception as e:
        return f"错误: {e}"

# 演示不同用途
print("🤖 AI助手演示 - 看看AI能为你做什么！")
print("=" * 60)

# 1. 编程助手
print("\n💻 1. 编程助手演示")
code_prompt = "写一个Python函数，可以检查密码强度（包含大小写字母、数字、特殊字符）"
print(f"问题: {code_prompt}")
print("AI回复:")
print(call_ai(code_prompt))

print("\n" + "=" * 60)

# 2. 写作助手
print("\n✍️ 2. 写作助手演示")
writing_prompt = "帮我写一封感谢邮件，感谢同事在项目中的帮助"
print(f"问题: {writing_prompt}")
print("AI回复:")
print(call_ai(writing_prompt))

print("\n" + "=" * 60)

# 3. 翻译助手
print("\n🌍 3. 翻译助手演示")
translation_prompt = "请将'人工智能正在改变我们的生活方式'翻译成英文，并解释翻译要点"
print(f"问题: {translation_prompt}")
print("AI回复:")
print(call_ai(translation_prompt))

print("\n" + "=" * 60)

# 4. 创意助手
print("\n🎨 4. 创意助手演示")
creative_prompt = "写一个关于程序员和AI成为朋友的有趣小故事，100字左右"
print(f"问题: {creative_prompt}")
print("AI回复:")
print(call_ai(creative_prompt, model="gpt-4o"))  # 使用更强的模型

print("\n" + "=" * 60)
print("🎉 演示完成！你可以看到AI在各个领域都能提供帮助！")
print("\n💡 现在你知道这些AI模型能做什么了：")
print("- 写代码和解决编程问题")
print("- 写各种文档和邮件")
print("- 翻译和语言处理")
print("- 创意写作和头脑风暴")
print("- 学习和答疑解惑")
print("\n🚀 你可以修改上面的prompt来测试你自己的问题！")
