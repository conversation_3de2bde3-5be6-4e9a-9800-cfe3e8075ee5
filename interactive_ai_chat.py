import requests
import os
from dotenv import load_dotenv
import json

# 加载环境变量
load_dotenv()

class InteractiveAIChat:
    def __init__(self):
        self.base_url = "https://models.inference.ai.azure.com"
        self.token = os.getenv("GITHUB_TOKEN")
        if not self.token:
            raise ValueError("请设置 GITHUB_TOKEN 环境变量")
        
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        self.models = {
            "1": "gpt-4o-mini",      # 推荐日常使用
            "2": "gpt-4o",           # 最强大
            "3": "gpt-3.5-turbo",    # 经典
            "4": "Phi-3-mini-4k-instruct"  # 轻量级
        }
    
    def chat_completion(self, model, messages, max_tokens=1000, temperature=0.7):
        """发送聊天完成请求"""
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        try:
            response = requests.post(url, json=data, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def show_menu(self):
        """显示功能菜单"""
        print("\n" + "="*60)
        print("AI助手 - 你想让我帮你做什么？")
        print("="*60)
        print("1. 编程助手 - 写代码、调试、解释")
        print("2. 写作助手 - 写邮件、文章、创意内容")
        print("3. 翻译助手 - 中英文翻译")
        print("4. 学习助手 - 解释概念、答疑解惑")
        print("5. 实用工具 - 文件整理、数据处理等")
        print("6. 创意助手 - 故事、诗歌、头脑风暴")
        print("7. 自由对话 - 随便聊聊")
        print("0. 退出")
        print("="*60)
    
    def select_model(self):
        """选择AI模型"""
        print("\n选择AI模型：")
        print("1. GPT-4o-mini (推荐，速度快)")
        print("2. GPT-4o (最强大，适合复杂任务)")
        print("3. GPT-3.5-turbo (经典模型)")
        print("4. Phi-3-mini (轻量级)")
        
        while True:
            choice = input("\n请选择模型 (1-4): ").strip()
            if choice in self.models:
                return self.models[choice]
            print("❌ 无效选择，请重新输入")
    
    def programming_assistant(self, model):
        """编程助手"""
        print("\n编程助手模式")
        print("你可以问我：")
        print("- 写一个Python函数来...")
        print("- 这段代码有什么问题？")
        print("- 解释一下这个概念...")

        user_input = input("\n请描述你的编程需求: ")

        messages = [
            {"role": "system", "content": "你是一个专业的编程助手，擅长各种编程语言和技术。请提供清晰、实用的代码和解释。"},
            {"role": "user", "content": user_input}
        ]

        return self.chat_completion(model, messages, max_tokens=800, temperature=0.3)
    
    def writing_assistant(self, model):
        """写作助手"""
        print("\n写作助手模式")
        print("我可以帮你：")
        print("- 写邮件、报告、文章")
        print("- 润色文字")
        print("- 创意写作")

        user_input = input("\n请告诉我你想写什么: ")

        messages = [
            {"role": "system", "content": "你是一个专业的写作助手，擅长各种文体的写作，语言流畅自然。"},
            {"role": "user", "content": user_input}
        ]

        return self.chat_completion(model, messages, max_tokens=600, temperature=0.6)
    
    def translation_assistant(self, model):
        """翻译助手"""
        print("\n翻译助手模式")
        text = input("请输入要翻译的文本: ")

        messages = [
            {"role": "system", "content": "你是一个专业的翻译助手，能够准确翻译中英文，并解释翻译要点。"},
            {"role": "user", "content": f"请翻译以下文本，并简要解释翻译要点：{text}"}
        ]

        return self.chat_completion(model, messages, max_tokens=400, temperature=0.3)
    
    def learning_assistant(self, model):
        """学习助手"""
        print("\n学习助手模式")
        question = input("请问你想了解什么概念或问题: ")

        messages = [
            {"role": "system", "content": "你是一个耐心的老师，善于用简单易懂的方式解释复杂概念，并提供实例。"},
            {"role": "user", "content": question}
        ]

        return self.chat_completion(model, messages, max_tokens=700, temperature=0.5)
    
    def utility_assistant(self, model):
        """实用工具助手"""
        print("\n实用工具模式")
        print("我可以帮你：")
        print("- 写脚本自动化任务")
        print("- 数据处理和分析")
        print("- 系统管理工具")

        task = input("请描述你需要的工具或脚本: ")

        messages = [
            {"role": "system", "content": "你是一个实用工具专家，擅长创建各种自动化脚本和实用工具。"},
            {"role": "user", "content": task}
        ]

        return self.chat_completion(model, messages, max_tokens=800, temperature=0.4)
    
    def creative_assistant(self, model):
        """创意助手"""
        print("\n创意助手模式")
        creative_task = input("请告诉我你的创意需求（故事、诗歌、创意方案等）: ")

        messages = [
            {"role": "system", "content": "你是一个富有创意的助手，擅长创作各种有趣的内容。"},
            {"role": "user", "content": creative_task}
        ]

        return self.chat_completion(model, messages, max_tokens=800, temperature=0.8)
    
    def free_chat(self, model):
        """自由对话"""
        print("\n自由对话模式")
        message = input("想聊什么: ")

        messages = [
            {"role": "user", "content": message}
        ]

        return self.chat_completion(model, messages, max_tokens=500, temperature=0.7)
    
    def run(self):
        """主运行循环"""
        print("欢迎使用AI助手！")

        while True:
            self.show_menu()
            choice = input("请选择功能 (0-7): ").strip()

            if choice == "0":
                print("再见！")
                break

            if choice not in ["1", "2", "3", "4", "5", "6", "7"]:
                print("无效选择，请重新输入")
                continue

            # 选择模型
            model = self.select_model()
            print(f"\n使用模型: {model}")

            # 根据选择执行对应功能
            if choice == "1":
                result = self.programming_assistant(model)
            elif choice == "2":
                result = self.writing_assistant(model)
            elif choice == "3":
                result = self.translation_assistant(model)
            elif choice == "4":
                result = self.learning_assistant(model)
            elif choice == "5":
                result = self.utility_assistant(model)
            elif choice == "6":
                result = self.creative_assistant(model)
            elif choice == "7":
                result = self.free_chat(model)

            # 显示结果
            print("\n" + "="*60)
            print("AI回复:")
            print("="*60)

            if "error" in result:
                print(f"错误: {result['error']}")
            else:
                print(result["choices"][0]["message"]["content"])
                print(f"\n使用tokens: {result['usage']['total_tokens']}")

            print("\n" + "="*60)
            input("按回车键继续...")

if __name__ == "__main__":
    try:
        chat = InteractiveAIChat()
        chat.run()
    except Exception as e:
        print(f"启动失败: {e}")
