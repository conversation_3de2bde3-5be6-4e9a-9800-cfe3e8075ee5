import requests
import os
from dotenv import load_dotenv
import json

# 加载环境变量
load_dotenv()

class GitHubModelsClient:
    def __init__(self):
        self.base_url = "https://models.inference.ai.azure.com"
        self.token = os.getenv("GITHUB_TOKEN")
        if not self.token:
            raise ValueError("请设置 GITHUB_TOKEN 环境变量")
        
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
    
    def chat_completion(self, model, messages, max_tokens=1000, temperature=0.7, stream=False):
        """发送聊天完成请求"""
        url = f"{self.base_url}/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream
        }
        
        response = requests.post(url, json=data, headers=self.headers)
        response.raise_for_status()
        
        if stream:
            return response
        else:
            return response.json()
    
    def list_available_models(self):
        """列出可用的模型"""
        models = [
            "gpt-4o",
            "gpt-4o-mini", 
            "gpt-3.5-turbo",
            "Phi-3-medium-4k-instruct",
            "Phi-3-mini-4k-instruct",
            "Meta-Llama-3-70B-Instruct",
            "Meta-Llama-3-8B-Instruct"
        ]
        return models

def main():
    """主函数 - 演示如何使用客户端"""
    client = GitHubModelsClient()
    
    print("🚀 GitHub Models API 客户端测试")
    print("=" * 50)
    
    # 示例1：简单对话
    print("\n📝 示例1：简单对话")
    messages = [
        {"role": "user", "content": "你好！请用一句话介绍Python编程语言。"}
    ]
    
    try:
        result = client.chat_completion("gpt-4o-mini", messages, max_tokens=100)
        print("AI回复：", result["choices"][0]["message"]["content"])
        print(f"使用的tokens: {result['usage']['total_tokens']}")
    except Exception as e:
        print(f"❌ 错误：{e}")
    
    # 示例2：代码生成
    print("\n💻 示例2：代码生成")
    messages = [
        {"role": "user", "content": "写一个Python函数来检查一个数字是否为质数"}
    ]
    
    try:
        result = client.chat_completion("gpt-4o-mini", messages, max_tokens=300, temperature=0.3)
        print("生成的代码：")
        print(result["choices"][0]["message"]["content"])
    except Exception as e:
        print(f"❌ 错误：{e}")
    
    # 示例3：列出可用模型
    print("\n�� 可用的模型：")
    models = client.list_available_models()
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")

if __name__ == "__main__":
    main()
