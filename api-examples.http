### GitHub Models API 示例 - 已配置你的 API KEY
### 直接点击下面的 "Send Request" 按钮即可测试

# API 配置
@baseUrl = https://models.inference.ai.azure.com
@token = *********************************************************************************************

### 1. 实用示例：文件整理工具
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "帮我写一个Python脚本，可以自动整理桌面文件，按文件类型分类到不同文件夹（图片、文档、视频等）"
    }
  ],
  "max_tokens": 800,
  "temperature": 0.3
}

###

### 2. 实用示例：写邮件助手
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "帮我写一封专业的邮件，向老板请假3天去参加朋友婚礼，语气要礼貌正式"
    }
  ],
  "max_tokens": 400,
  "temperature": 0.5
}

###

### 3. 实用示例：学习助手
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "system",
      "content": "你是一个耐心的编程老师，善于用简单易懂的方式解释复杂概念。"
    },
    {
      "role": "user",
      "content": "我是编程新手，请用通俗易懂的语言解释什么是API，并举几个生活中的例子"
    }
  ],
  "max_tokens": 600,
  "temperature": 0.7
}

###

### 4. 实用示例：翻译助手
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "请将以下中文翻译成地道的英文，并解释为什么这样翻译：'这个项目的进度比预期的要快，我们可能需要提前准备下一阶段的工作。'"
    }
  ],
  "max_tokens": 300,
  "temperature": 0.3
}

###

### 5. 实用示例：创意写作助手
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "user",
      "content": "帮我写一个有趣的短故事，主题是：一个程序员发现他写的代码居然有了自己的意识"
    }
  ],
  "max_tokens": 800,
  "temperature": 0.8,
  "stream": true
}

###
