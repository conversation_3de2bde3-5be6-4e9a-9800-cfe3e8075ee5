### GitHub Models API 示例 - 已配置你的 API KEY
### 直接点击下面的 "Send Request" 按钮即可测试

# API 配置
@baseUrl = https://models.inference.ai.azure.com
@token = *********************************************************************************************

### 1. 测试连接 - 使用 GPT-4o-mini 模型
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "你好！请简单介绍一下你自己。"
    }
  ],
  "max_tokens": 200,
  "temperature": 0.7
}

###

### 2. 代码生成示例 - Python 函数
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "写一个Python函数来计算斐波那契数列的第n项"
    }
  ],
  "max_tokens": 500,
  "temperature": 0.3
}

###

### 3. 使用 GPT-4o 模型（更强大但消耗更多配额）
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的软件开发助手。"
    },
    {
      "role": "user",
      "content": "解释JavaScript中的异步编程概念，包括Promise和async/await"
    }
  ],
  "max_tokens": 1000,
  "temperature": 0.6
}

###

### 4. 使用 Phi-3 模型
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "Phi-3-mini-4k-instruct",
  "messages": [
    {
      "role": "user",
      "content": "创建一个简单的HTML页面，包含一个按钮和点击事件"
    }
  ],
  "max_tokens": 600,
  "temperature": 0.4
}

###

### 5. 流式响应示例（实时获取回复）
POST {{baseUrl}}/chat/completions
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "model": "gpt-4o-mini",
  "messages": [
    {
      "role": "user",
      "content": "详细解释React Hooks的使用方法和最佳实践"
    }
  ],
  "max_tokens": 1200,
  "temperature": 0.5,
  "stream": true
}

###
